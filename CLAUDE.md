# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

GIHD School Management System - A full-stack Django REST + Next.js application for managing educational institutions in Pakistan.

## Development Commands

### Backend (Django)
```bash
# Navigate to backend
cd backend

# Create/activate virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt

# Database migrations
python manage.py makemigrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Run development server
python manage.py runserver 5001

# Run tests
python manage.py test

# Generate test data
python create_test_data.py

# Management commands
python manage.py bulk_import_students <file_path>
python manage.py generate_monthly_report
```

### Frontend (Next.js)
```bash
# Navigate to frontend
cd frontend

# Install dependencies
npm install

# Run development server
npm run dev

# Build for production
npm run build

# Run production build
npm run start

# Run linting
npm run lint
```

## Architecture Overview

### Backend Structure
- **Django REST Framework** with JWT authentication
- **Apps**:
  - `authentication`: User management and JWT tokens
  - `students`: Student profiles, guardians, and notes
  - `academics`: Academic years, programs, sessions, enrollments
  - `fees`: Fee structures, payments, installments
  - `documents`: Document management
  - `reports`: Financial and academic reporting
  - `common`: Shared utilities, pagination, response formatting

### Frontend Structure
- **Next.js 15** with TypeScript
- **Key Directories**:
  - `app/`: Next.js app router pages
  - `components/`: UI components using shadcn/ui
  - `services/`: API service layer with standardized error handling
  - `contexts/`: React contexts (auth)
  - `hooks/`: Custom React hooks

### API Communication
- Base URL: `http://localhost:5001/api`
- Standardized response format:
  ```typescript
  {
    status: 'success' | 'error',
    http_code: number,
    message: string,
    data?: any
  }
  ```
- JWT authentication with Bearer tokens
- CORS configured for localhost:3000

### Key Models
- **Student**: Pakistani-specific fields (CNIC, B-Form), auto-generated IDs (GIHD2024xxxx)
- **Guardian**: Multiple guardian types per student
- **AcademicSession**: Year/program/session combinations
- **Enrollment**: Links students to sessions with status tracking
- **FeeStructure**: Configurable fees per session
- **Payment**: Tracks payments with installment support

### Database
- SQLite for development (`db.sqlite3`)
- Django Simple History for audit trails
- Indexes on frequently queried fields

### Authentication Flow
1. Login via `/api/auth/login/` returns access/refresh tokens
2. Frontend stores tokens and sets Authorization header
3. Protected routes check authentication status
4. Token refresh handled automatically

### Pakistani Context
- CNIC format validation: `12345-1234567-1`
- Phone format: `+923001234567` or `03001234567`
- Province choices include all Pakistani provinces/territories
- Currency handling in PKR
- Islamic calendar considerations for academic sessions

Frontend develsopment erver is running on port 5000 on systemctl
Backend develsopment erver is running on port 5001 on systemctl