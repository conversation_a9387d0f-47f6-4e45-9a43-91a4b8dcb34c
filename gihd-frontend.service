[Unit]
Description=GIHD School Management System Frontend
After=network.target

[Service]
Type=simple
User=ali
Group=ali
WorkingDirectory=/home/<USER>/development/GIHD_SCHOOL/frontend
Environment=PATH=/home/<USER>/.nvm/versions/node/v24.4.1/bin:/usr/bin:/bin:/usr/sbin:/sbin:/usr/local/bin
Environment=NODE_ENV=production
Environment=PORT=5000
Environment=NEXT_PUBLIC_API_URL=http://localhost:5001/api
ExecStartPre=/home/<USER>/.nvm/versions/node/v24.4.1/bin/pnpm run build
ExecStart=/home/<USER>/.nvm/versions/node/v24.4.1/bin/pnpm start
ExecReload=/bin/kill -s HUP $MAINPID
KillMode=mixed
TimeoutStopSec=5
PrivateTmp=true
Restart=on-failure
RestartSec=3

[Install]
WantedBy=multi-user.target