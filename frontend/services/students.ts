import { BaseApiService } from './base-api';
import { tokenManager } from './token-manager';

export interface Student {
  id?: number;
  first_name: string;
  last_name: string;
  date_of_birth: string;
  gender: 'M' | 'F';
  admission_number: string;
  class_level: string;
  section: string;
  phone_number?: string;
  email?: string;
  address?: string;
  guardian_name?: string;
  guardian_phone?: string;
  guardian_email?: string;
  admission_date: string;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface StudentListParams {
  page?: number;
  limit?: number;
  search?: string;
  class_level?: string;
  section?: string;
  is_active?: boolean;
}

export interface StudentListResponse {
  results: Student[];
  count: number;
  next?: string;
  previous?: string;
}

export class StudentsService extends BaseApiService {
  private readonly endpoint = '/students';

  constructor() {
    super();
    // Register this service with the token manager
    tokenManager.registerService(this);
  }

  async getStudents(params?: StudentListParams) {
    const queryParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }

    const query = queryParams.toString();
    const url = query ? `${this.endpoint}/?${query}` : `${this.endpoint}/`;
    
    return this.get<StudentListResponse>(url);
  }

  async getStudent(id: number) {
    return this.get<Student>(`${this.endpoint}/${id}/`);
  }

  async createStudent(student: Omit<Student, 'id' | 'created_at' | 'updated_at'>) {
    return this.post<Student>(`${this.endpoint}/`, student);
  }

  async updateStudent(id: number, student: Partial<Student>) {
    return this.put<Student>(`${this.endpoint}/${id}/`, student);
  }

  async patchStudent(id: number, student: Partial<Student>) {
    return this.patch<Student>(`${this.endpoint}/${id}/`, student);
  }

  async deleteStudent(id: number) {
    return this.delete(`${this.endpoint}/${id}/`);
  }

  async bulkImportStudents(studentsData: Omit<Student, 'id' | 'created_at' | 'updated_at'>[]) {
    return this.post<{ success: number; errors: any[] }>(`${this.endpoint}/bulk-import/`, {
      students: studentsData
    });
  }

  async getStudentsByClass(classLevel: string, section?: string) {
    const params: StudentListParams = { class_level: classLevel };
    if (section) params.section = section;
    return this.getStudents(params);
  }

  async searchStudents(query: string) {
    return this.getStudents({ search: query });
  }

  async activateStudent(id: number) {
    return this.patch<Student>(`${this.endpoint}/${id}/`, { is_active: true });
  }

  async deactivateStudent(id: number) {
    return this.patch<Student>(`${this.endpoint}/${id}/`, { is_active: false });
  }
}

export const studentsService = new StudentsService();