from rest_framework import serializers
from .models import Student, Guardian, StudentNote


class StudentSerializer(serializers.ModelSerializer):
    """Serializer for Student model with all fields"""
    
    # Read-only fields
    student_id = serializers.CharField(read_only=True)
    age = serializers.SerializerMethodField()
    full_name = serializers.SerializerMethodField()
    display_name = serializers.SerializerMethodField()
    
    class Meta:
        model = Student
        fields = [
            # Basic Information
            'id', 'student_id', 'first_name', 'last_name', 'father_name',
            'date_of_birth', 'gender', 'photo', 'age', 'full_name', 'display_name',

            # Identity Documents
            'cnic', 'b_form_number',

            # Contact Information
            'phone_number', 'email',

            # Address
            'address_line_1', 'address_line_2', 'city', 'province', 'postal_code',

            # Personal Details
            'religion', 'nationality', 'marital_status', 'blood_group',

            # Medical Information
            'medical_conditions', 'allergies', 'emergency_medical_contact',

            # Academic Information
            'admission_date', 'previous_education', 'matric_marks', 'intermediate_marks',

            # Status
            'is_active',

            # Timestamps
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'student_id', 'created_at', 'updated_at']
    
    def get_age(self, obj):
        return obj.age()
    
    def get_full_name(self, obj):
        return obj.get_full_name()
    
    def get_display_name(self, obj):
        return obj.get_display_name()
    
    def validate_cnic(self, value):
        """Validate CNIC format"""
        import re
        if value and not re.match(r'^\d{5}-\d{7}-\d{1}$', value):
            raise serializers.ValidationError("CNIC must be in format: 12345-1234567-1")
        return value
    
    def validate_phone_number(self, value):
        """Validate Pakistani phone number format"""
        if value:
            # Remove spaces and dashes
            clean_number = value.replace(' ', '').replace('-', '')
            if not (clean_number.startswith('+92') or clean_number.startswith('03')):
                raise serializers.ValidationError("Phone number must be in Pakistani format")
        return value


class GuardianSerializer(serializers.ModelSerializer):
    """Serializer for Guardian model"""
    
    class Meta:
        model = Guardian
        fields = [
            'id', 'student', 'guardian_type', 'first_name', 'last_name',
            'relationship', 'cnic', 'phone_number', 'alternate_phone', 'email',
            'address_line_1', 'address_line_2', 'city', 'province',
            'occupation', 'workplace', 'monthly_income',
            'can_pickup_student', 'is_emergency_contact', 'is_active',
            'preferred_contact_method', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class StudentNoteSerializer(serializers.ModelSerializer):
    """Serializer for StudentNote model"""
    
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = StudentNote
        fields = [
            'id', 'student', 'note_type', 'title', 'content',
            'is_private', 'created_by', 'created_by_name',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_by', 'created_at', 'updated_at']
    
    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class StudentCreateSerializer(serializers.ModelSerializer):
    """Simplified serializer for creating students with minimal required fields"""

    class Meta:
        model = Student
        fields = [
            'first_name', 'last_name', 'father_name', 'date_of_birth', 'gender',
            'phone_number', 'email', 'address_line_1', 'city', 'province',
            'admission_date', 'nationality', 'religion'
        ]
    
    def create(self, validated_data):
        # Set default values for required fields if not provided
        if 'father_name' not in validated_data:
            validated_data['father_name'] = 'N/A'
        if 'admission_date' not in validated_data:
            from datetime import date
            validated_data['admission_date'] = date.today()
        if 'nationality' not in validated_data:
            validated_data['nationality'] = 'Pakistani'

        return super().create(validated_data)
